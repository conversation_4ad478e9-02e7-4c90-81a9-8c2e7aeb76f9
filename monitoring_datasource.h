#ifndef MONITORING_DATASOURCE_H
#define MONITORING_DATASOURCE_H

#include <QObject>
#include <QTimer>
#include <QVariantList>
#include <QDateTime>
#include <QThread>
#include <QMutex>
#include <QThreadPool>
#include <QVector>

QT_BEGIN_NAMESPACE
QT_END_NAMESPACE

// Qt 5.x forward declaration
namespace QtCharts {
    class QAbstractSeries;
}

class MonitoringDataSource : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QVariantList smokeO2Data READ smokeO2Data NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeCOData READ smokeCOData NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeSwitch1Data READ smokeSwitch1Data NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeTableData READ smokeTableData NOTIFY smokeTableDataChanged)
    Q_PROPERTY(QStringList boilerList READ boilerList NOTIFY boilerListChanged)
    Q_PROPERTY(QString currentBoiler READ currentBoiler WRITE setCurrentBoiler NOTIFY currentBoilerChanged)
    Q_PROPERTY(bool isRunning READ isRunning WRITE setIsRunning NOTIFY isRunningChanged)
    Q_PROPERTY(bool isDataConnected READ isDataConnected NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString connectionStatus READ connectionStatus NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString currentTemperature READ currentTemperature NOTIFY currentDataChanged)
    Q_PROPERTY(QString currentVoltage READ currentVoltage NOTIFY currentDataChanged)
    Q_PROPERTY(QString currentCurrent READ currentCurrent NOTIFY currentDataChanged)

public:
    // 高效数据结构定义 - 必须在public部分以供QVector使用
    struct DataPoint {
        double timeHours;
        double timeMinutes;
        double value;

        DataPoint() : timeHours(0), timeMinutes(0), value(0) {}
        DataPoint(double h, double m, double v) : timeHours(h), timeMinutes(m), value(v) {}
    };

    explicit MonitoringDataSource(QObject *parent = nullptr);

    // 属性访问器 - 动态转换优化数据为QML兼容格式
    QVariantList smokeO2Data() const;
    QVariantList smokeCOData() const;
    QVariantList smokeSwitch1Data() const;
    QVariantList smokeTableData() const { return m_smokeTableData; }
    QStringList boilerList() const { return m_boilerList; }
    QString currentBoiler() const { return m_currentBoiler; }
    bool isRunning() const { return m_isRunning; }
    bool isDataConnected() const { return m_isDataConnected; }
    QString connectionStatus() const { return m_connectionStatus; }
    QString currentTemperature() const { return m_currentTemperature; }
    QString currentVoltage() const { return m_currentVoltage; }
    QString currentCurrent() const { return m_currentCurrent; }

    // 获取当前设备的采集间隔（秒）
    Q_INVOKABLE int getCurrentCollectionInterval() const;

    void setIsRunning(bool running);
    void setCurrentBoiler(const QString &boiler);

public slots:
    void startMonitoring();
    void stopMonitoring();
    void clearData();
    void updateSmokeChartSeries(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex = 0);
    void updateSmokeChartSeriesWithMinutes(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries);
    void updateSmokeChartSeriesWithScroll(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex, double scrollOffset);
    void updateChartIncremental(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex);



signals:
    void smokeDataChanged();
    void smokeTableDataChanged();
    void boilerListChanged();
    void currentBoilerChanged();
    void isRunningChanged();
    void chartDataUpdated();
    void dataConnectionChanged();
    void currentDataChanged();
    // boilerSwitchCompleted信号已移除，使用简化的切换逻辑

private slots:
    void updateData();

private:
    void updateSmokeData();
    void loadBoilerList();
    void reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler);
    void addSmokeTableRow(double o2, double co, double temperature, double voltage, double current, int switch1);
    void updateTimerInterval();

    // 渐进式数据管理方法（纯优化版本）
    void addDataPoint(double o2, double co, int switch1, double relativeTimeHours, double relativeTimeMinutes);
    void performMemoryCleanup();     // 执行内存清理
    void clearAllData();             // 清空所有数据

    // 增量图表更新方法
    void appendNewChartPoint(QtCharts::QAbstractSeries *series, double x, double y, int maxPoints);

    // 高效数据访问方法
    void addDataPointOptimized(double o2, double co, int switch1, double relativeTimeHours, double relativeTimeMinutes);
    void maintainDataWindowOptimized();

    // 异步锅炉切换相关方法已移除，使用简化的切换逻辑
    
    QTimer *m_timer;

    // 高效数据容器 - 纯优化版本
    QVector<DataPoint> m_o2DataOptimized;      // 氧气数据高效存储
    QVector<DataPoint> m_coDataOptimized;      // 一氧化碳数据高效存储
    QVector<DataPoint> m_switch1DataOptimized; // Switch1数据高效存储

    // 数据窗口管理
    QDateTime m_windowStartTime;     // 数据窗口开始时间
    int m_totalDataPoints;           // 总数据点计数器
    int m_lastCleanupCount;          // 上次清理时的数据点数

    // 表格数据
    QVariantList m_smokeTableData;

    // 表格更新计数器 - 每三次采集更新一次表格
    int m_tableUpdateCounter;

    // 烟气分析仪相关
    QStringList m_boilerList;  // 保持变量名以兼容现有代码
    QString m_currentBoiler;   // 保持变量名以兼容现有代码

    // 反吹反馈控制相关
    bool m_isBackflowActive;           // 反吹反馈是否激活
    bool m_isDataUpdateSuspended;      // 氧气和一氧化碳数据更新是否暂停
    QTimer *m_backflowDelayTimer;      // 反吹反馈延迟恢复定时器
    int m_backflowDelayTime;           // 延迟时间（秒）
    QString m_suspendedO2Value;        // 暂停时保存的氧气浓度值
    QString m_suspendedCOValue;        // 暂停时保存的一氧化碳浓度值

    // 反吹反馈控制方法
    void checkBackflowStatus(int switch1);
    void suspendO2COUpdates();
    void resumeO2COUpdates();
    int getBackflowDelayTime() const;

    bool m_isRunning;
    bool m_isDataConnected;
    QString m_connectionStatus;
    int m_dataCount;
    bool m_isStopping;  // 防重复停止标志
    
    // 当前数据值
    QString m_currentTemperature;
    QString m_currentVoltage;
    QString m_currentCurrent;

    // 相对时间轴相关变量
    QDateTime m_dataStartTime;  // 数据采集开始时间
    bool m_dataStartTimeSet;    // 是否已设置开始时间

    // 异步操作保护变量已移除，使用简化的切换逻辑

    // 32位程序优化的数据管理常量定义 - 减少内存占用，提升性能
    static const int MAX_DISPLAY_POINTS = 400;   // 图表最大显示点数（32位优化）
    static const int DATA_WINDOW_HOURS = 25;     // 数据窗口：保留最近25小时（支持24小时视图）
    static const int MAX_MEMORY_POINTS = 15000;  // 内存中最大保存点数（32位优化：减半）
    static const int CLEANUP_THRESHOLD = 2000;   // 触发清理的阈值点数（32位优化：更频繁清理）

    // 图表更新优化常量（32位程序优化）
    static const int CHART_UPDATE_INTERVAL_MS = 30000;  // 图表更新间隔：30秒
    static const int INCREMENTAL_UPDATE_BATCH = 5;      // 增量更新批次大小（32位优化：减少批次）

    // 缩放级别的显示点数限制（32位程序优化）
    static const int ZOOM_24H_POINTS = 150;     // 24小时视图：150点（32位优化）
    static const int ZOOM_12H_POINTS = 200;     // 12小时视图：200点（32位优化）
    static const int ZOOM_8H_POINTS = 250;      // 8小时视图：250点（32位优化）
    static const int ZOOM_1H_POINTS = 300;      // 1小时视图：300点（32位优化）

    static const int MAX_TABLE_ROWS = 5;
    static const int TABLE_UPDATE_INTERVAL = 3;  // 每3次采集更新一次表格
};

#endif // MONITORING_DATASOURCE_H
