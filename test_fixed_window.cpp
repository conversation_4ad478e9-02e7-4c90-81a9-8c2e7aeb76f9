// 测试固定时间窗口功能的简单验证程序
#include <iostream>
#include <vector>
#include <algorithm>

struct DataPoint {
    double timeHours;
    double timeMinutes;
    double value;
    
    DataPoint(double h, double m, double v) : timeHours(h), timeMinutes(m), value(v) {}
};

void testFixedWindow() {
    std::vector<DataPoint> testData;
    
    // 模拟25小时的数据（每小时一个点）
    for (int i = 0; i < 25; i++) {
        testData.emplace_back(i, i * 60, i * 2.5);  // 时间和数值都递增
    }
    
    std::cout << "=== 测试固定时间窗口逻辑 ===" << std::endl;
    std::cout << "总数据点数: " << testData.size() << std::endl;
    
    // 测试24小时窗口
    double displayRangeHours = 24.0;
    double windowEndTime = testData.back().timeHours;  // 最新时间：24小时
    double windowStartTime = std::max(0.0, windowEndTime - displayRangeHours);  // 0小时
    
    std::cout << "\n24小时窗口测试:" << std::endl;
    std::cout << "窗口范围: [" << windowStartTime << ", " << windowEndTime << "] 小时" << std::endl;
    
    int pointsInWindow = 0;
    for (const auto& point : testData) {
        if (point.timeHours >= windowStartTime && point.timeHours <= windowEndTime) {
            pointsInWindow++;
        }
    }
    std::cout << "窗口内数据点: " << pointsInWindow << " (应该是25个)" << std::endl;
    
    // 测试12小时窗口
    displayRangeHours = 12.0;
    windowStartTime = std::max(0.0, windowEndTime - displayRangeHours);  // 12小时
    
    std::cout << "\n12小时窗口测试:" << std::endl;
    std::cout << "窗口范围: [" << windowStartTime << ", " << windowEndTime << "] 小时" << std::endl;
    
    pointsInWindow = 0;
    for (const auto& point : testData) {
        if (point.timeHours >= windowStartTime && point.timeHours <= windowEndTime) {
            pointsInWindow++;
        }
    }
    std::cout << "窗口内数据点: " << pointsInWindow << " (应该是13个)" << std::endl;
    
    // 测试1小时窗口（分钟数据）
    std::vector<DataPoint> minuteData;
    for (int i = 0; i < 90; i++) {  // 90分钟的数据
        minuteData.emplace_back(i / 60.0, i, i * 0.1);
    }
    
    double windowEndMinutes = minuteData.back().timeMinutes;  // 89分钟
    double windowStartMinutes = std::max(0.0, windowEndMinutes - 60.0);  // 29分钟
    
    std::cout << "\n1小时窗口测试（分钟数据）:" << std::endl;
    std::cout << "窗口范围: [" << windowStartMinutes << ", " << windowEndMinutes << "] 分钟" << std::endl;
    
    pointsInWindow = 0;
    for (const auto& point : minuteData) {
        if (point.timeMinutes >= windowStartMinutes && point.timeMinutes <= windowEndMinutes) {
            pointsInWindow++;
        }
    }
    std::cout << "窗口内数据点: " << pointsInWindow << " (应该是61个)" << std::endl;
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
}

int main() {
    testFixedWindow();
    return 0;
}
