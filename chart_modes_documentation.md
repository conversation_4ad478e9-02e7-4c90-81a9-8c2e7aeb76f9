# 图表显示模式说明

## 概述

根据您的需求，我们实现了两种图表显示模式：

1. **实时数据模式** - 固定时间窗口，不滚动
2. **历史数据模式** - 支持滚动查看历史数据

## 模式详细说明

### 1. 实时数据模式（固定窗口）

**特点：**
- 只显示最近的指定时长数据
- 不需要滚动条
- 时间轴固定，数据在窗口内动态更新
- 内存使用更少，性能更好

**时间窗口：**
- 24小时视图：只显示最近24小时
- 12小时视图：只显示最近12小时  
- 8小时视图：只显示最近8小时
- 1小时视图：只显示最近60分钟

**使用方法：**
```cpp
// C++ 调用
monitoringDataSource->updateRealTimeChartSeries(o2Series, coSeries, zoomIndex);
monitoringDataSource->updateRealTimeChartSeriesWithMinutes(o2Series, coSeries); // 1小时视图
```

```qml
// QML 调用
monitoringDataSource.updateRealTimeChartSeries(o2Series, coSeries, currentZoomIndex)
monitoringDataSource.updateRealTimeChartSeriesWithMinutes(o2Series, coSeries) // 1小时视图
```

### 2. 历史数据模式（可滚动）

**特点：**
- 支持查看所有历史数据
- 有滚动条，可以查看任意时间段
- 当数据超过显示范围时，会自动滚动
- 适合数据分析和历史回顾

**滚动行为：**
- 当数据时长超过显示范围时，图表会向左滚动
- 最新数据始终显示在右侧
- 可以通过滚动查看历史数据

**使用方法：**
```cpp
// C++ 调用
monitoringDataSource->updateSmokeChartSeries(o2Series, coSeries, zoomIndex);
monitoringDataSource->updateSmokeChartSeriesWithMinutes(o2Series, coSeries); // 1小时视图
```

```qml
// QML 调用
monitoringDataSource.updateSmokeChartSeries(o2Series, coSeries, currentZoomIndex)
monitoringDataSource.updateSmokeChartSeriesWithMinutes(o2Series, coSeries) // 1小时视图
```

## 性能对比

| 特性 | 实时模式 | 历史模式 |
|------|----------|----------|
| 内存使用 | 低（只处理窗口内数据） | 高（处理所有数据） |
| CPU使用 | 低（数据量固定） | 中等（数据量随时间增长） |
| 滚动功能 | 无 | 有 |
| 适用场景 | 实时监控 | 数据分析 |

## 实现细节

### 核心差异

**实时模式：**
```cpp
// 固定时间窗口计算
double windowEndTime = lastPoint.timeHours;
double windowStartTime = qMax(0.0, windowEndTime - displayRangeHours);

// 只处理窗口内的数据
if (dataTime >= windowStartTime && dataTime <= windowEndTime) {
    qreal displayTime = dataTime - windowStartTime;  // 相对时间
    // 添加数据点...
}
```

**历史模式：**
```cpp
// 滚动偏移计算
double timeOffset = 0.0;
if (currentTime > displayRangeHours) {
    timeOffset = currentTime - displayRangeHours;
}

// 处理所有数据，应用偏移
qreal displayTime = absoluteTime - timeOffset;
if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
    // 添加数据点...
}
```

## 使用建议

### 实时监控场景
- 使用实时模式
- 设置定时器定期更新（建议2-5秒间隔）
- 适合操作员实时监控设备状态

### 数据分析场景
- 使用历史模式
- 可以手动触发更新
- 适合工程师分析历史趋势

### 混合使用
可以在同一个应用中提供模式切换：
```qml
property bool isRealTimeMode: true

function updateChart() {
    if (isRealTimeMode) {
        monitoringDataSource.updateRealTimeChartSeries(o2Series, coSeries, zoomIndex)
    } else {
        monitoringDataSource.updateSmokeChartSeries(o2Series, coSeries, zoomIndex)
    }
}
```

## 注意事项

1. **数据一致性**：两种模式使用相同的数据源，确保数据一致性
2. **性能优化**：实时模式已经过优化，适合长期运行
3. **内存管理**：实时模式有助于控制内存使用，避免长期运行时的内存泄漏
4. **用户体验**：根据用户需求选择合适的模式，提供清晰的模式指示

## 总结

这种双模式设计既满足了实时监控的性能需求（固定窗口），又保留了历史数据分析的灵活性（可滚动），是一个平衡性能和功能的优秀解决方案。
