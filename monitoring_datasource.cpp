#include "monitoring_datasource.h"
#include <QVariantMap>
#include <QtCharts/QXYSeries>
#include <QtCharts/QLineSeries>
#include <QCoreApplication>
#include <QtCharts/QAbstractSeries>
#include <QDateTime>
#include <QTimer>
#include <QMutexLocker>
#include <QThreadPool>
#include <QRunnable>
#include <QMetaObject>
#include <thread>
#include <chrono>

// Qt 5.x需要使用QtCharts命名空间
QT_CHARTS_USE_NAMESPACE

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

// BoilerSwitchTask类已移除，因为使用简化的锅炉切换逻辑

// 条件编译：启用硬件数据采集
#define ENABLE_HARDWARE_DATA
#ifdef ENABLE_HARDWARE_DATA
#include "smoke_analyzer_comm.h"
#include "boiler.h"
#include "config_manager.h"
#endif

MonitoringDataSource::MonitoringDataSource(QObject *parent)
    : QObject(parent)
    , m_timer(new QTimer(this))
    , m_tableUpdateCounter(0)
    , m_isBackflowActive(false)
    , m_isDataUpdateSuspended(false)
    , m_backflowDelayTimer(new QTimer(this))
    , m_backflowDelayTime(60)
    , m_suspendedO2Value("0.00%")
    , m_suspendedCOValue("0ppm")
    , m_isRunning(false)
    , m_isDataConnected(false)
    , m_connectionStatus("未连接串口数据采集设备")
    , m_dataCount(0)
    , m_isStopping(false)
    , m_currentTemperature("0.0℃")
    , m_currentVoltage("0.0kPa")
    , m_currentCurrent("0.000A")
    , m_dataStartTimeSet(false)
    , m_totalDataPoints(0)
    , m_lastCleanupCount(0)
    , m_resourceProtectionEnabled(true)
    , m_criticalErrorCount(0)
    , m_lastMemoryCheck(QDateTime::currentDateTime())
{
    connect(m_timer, &QTimer::timeout, this, &MonitoringDataSource::updateData);
    // 不设置初始间隔，等待从配置文件读取

    // 初始化反吹反馈延迟定时器
    m_backflowDelayTimer->setSingleShot(true);
    connect(m_backflowDelayTimer, &QTimer::timeout, this, &MonitoringDataSource::resumeO2COUpdates);

    // 加载锅炉列表
    loadBoilerList();

    // 设置初始定时器间隔（从配置文件读取）
    updateTimerInterval();

    // 启用资源保护机制
    enableResourceProtection();
}

void MonitoringDataSource::setIsRunning(bool running)
{
    if (m_isRunning != running) {
        m_isRunning = running;
        emit isRunningChanged();

        if (running) {
            startMonitoring();
        } else {
            stopMonitoring();
        }
    }
}

void MonitoringDataSource::setCurrentBoiler(const QString &boiler)
{
    if (m_currentBoiler != boiler) {
        debug_printf("切换烟气分析仪: 从 '%s' 到 '%s'\n",
                    m_currentBoiler.toStdString().c_str(), boiler.toStdString().c_str());

        // 简化的烟气分析仪切换逻辑，因为每个设备都有独立线程在采集数据
        m_currentBoiler = boiler;

        // 更新定时器间隔以匹配新锅炉的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentBoilerChanged();

        // 清空历史数据，准备显示新设备的数据
        clearAllData();  // 使用统一的清空方法
        m_smokeTableData.clear();
        m_dataCount = 0;
        m_tableUpdateCounter = 0;  // 重置表格更新计数器

        // 重置相对时间轴的开始时间
        m_dataStartTimeSet = false;

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "正在检测新烟气分析仪的串口连接...";
        emit dataConnectionChanged();

        // 发射数据变化信号
        emit smokeDataChanged();
        emit smokeTableDataChanged();
        emit chartDataUpdated();

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            updateData();
        }
    }
}

void MonitoringDataSource::startMonitoring()
{
    if (!m_timer->isActive()) {
        m_timer->start();
        m_isRunning = true;
        emit isRunningChanged();

        // 初始化连接状态检查
        m_isDataConnected = false;
        m_connectionStatus = "正在检测串口数据采集设备...";
        emit dataConnectionChanged();

        // 立即进行一次数据更新和连接状态检测，不等待定时器
        debug_printf("UI监控启动: 立即进行首次连接状态检测\n");
        updateData();

        // 基于配置的动态延迟更新数据，确保图表能正常显示
        int collectionInterval = getCurrentCollectionInterval();
        int firstDelay = collectionInterval * 1000;      // 第一次延迟：1个采集周期
        int secondDelay = collectionInterval * 1500;     // 第二次延迟：1.5个采集周期

        debug_printf("UI监控: 使用动态延迟 - 第一次=%d毫秒, 第二次=%d毫秒\n",
                    firstDelay, secondDelay);

        QTimer::singleShot(firstDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%d秒后再次更新数据\n", collectionInterval);
            updateData();
        });

        QTimer::singleShot(secondDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%.1f秒后第三次更新数据\n", collectionInterval * 1.5);
            updateData();
        });
    }
}



void MonitoringDataSource::stopMonitoring()
{
    // 防重复调用保护
    if (m_isStopping) {
        return;
    }

    if (m_timer->isActive()) {
        m_isStopping = true;

        m_timer->stop();
        m_isRunning = false;

        // 停止监控时重置连接状态
        m_isDataConnected = false;
        m_connectionStatus = "数据监控已停止";

        // 延迟发射信号，避免阻塞页面切换
        QTimer::singleShot(0, this, [this]() {
            emit isRunningChanged();
            emit dataConnectionChanged();

            // 重置保护标志，允许下次正常启动/停止
            m_isStopping = false;
        });
    } else {
        // 如果定时器已经停止，直接重置标志
        m_isStopping = false;
    }
}

int MonitoringDataSource::getCurrentCollectionInterval() const
{
    // 获取当前锅炉的采集间隔
    int interval = 15;  // 默认值

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            interval = g_config_manager->get<int>(m_currentBoiler.toStdString(), "CollectionInterval", 15);
        }
    }

    return interval;
}

int MonitoringDataSource::getBackflowDelayTime() const
{
    // 从配置文件读取反吹反馈延迟时间
    int delayTime = 60;  // 默认60秒

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            delayTime = g_config_manager->get<int>(m_currentBoiler.toStdString(), "BackflowDelayTime", 60);
        }
    }

    return delayTime;
}

void MonitoringDataSource::checkBackflowStatus(int switch1)
{
    bool currentBackflowActive = (switch1 == 1);  // 修正：1表示反吹运行，0表示停止

    // 检查反吹反馈状态是否发生变化
    if (currentBackflowActive != m_isBackflowActive) {
        m_isBackflowActive = currentBackflowActive;

        if (m_isBackflowActive) {
            // 反吹反馈开始运行，暂停氧气和一氧化碳数值更新
            suspendO2COUpdates();
            debug_printf("监控系统: 检测到反吹反馈开始运行，暂停氧气和一氧化碳数值更新\n");
        } else {
            // 反吹反馈停止，启动延迟恢复定时器
            m_backflowDelayTime = getBackflowDelayTime();
            m_backflowDelayTimer->start(m_backflowDelayTime * 1000);  // 转换为毫秒
            debug_printf("监控系统: 检测到反吹反馈停止，将在%d秒后恢复氧气和一氧化碳数值更新\n", m_backflowDelayTime);
        }
    }
}

void MonitoringDataSource::suspendO2COUpdates()
{
    if (!m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = true;

        // 停止延迟恢复定时器（如果正在运行）
        if (m_backflowDelayTimer->isActive()) {
            m_backflowDelayTimer->stop();
        }

        // 保存当前的O2和CO数值作为暂停前的最后数值
        // 注意：这里的数值会在checkBackflowStatus调用之前通过updateSmokeData获取到
        debug_printf("监控系统: 氧气和一氧化碳数值更新已暂停，保存的暂停前数值 - O2: %s, CO: %s\n",
                    m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
    }
}

void MonitoringDataSource::resumeO2COUpdates()
{
    if (m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = false;
        debug_printf("监控系统: 延迟%d秒后，氧气和一氧化碳数值更新已恢复\n", m_backflowDelayTime);

        // 立即触发一次数据更新，以显示最新的氧气和一氧化碳数值
        updateData();
    }
}

void MonitoringDataSource::clearData()
{
    // 清空所有数据
    clearAllData();

    m_smokeTableData.clear();
    m_dataCount = 0;
    m_tableUpdateCounter = 0;  // 重置表格更新计数器

    // 重置相对时间轴的开始时间
    m_dataStartTimeSet = false;

    emit smokeDataChanged();
    emit smokeTableDataChanged();
    emit chartDataUpdated();
}

void MonitoringDataSource::clearAllData()
{
    // 只清空优化数据结构
    m_o2DataOptimized.clear();
    m_coDataOptimized.clear();
    m_switch1DataOptimized.clear();

    // 重置窗口管理变量
    m_windowStartTime = QDateTime();
    m_totalDataPoints = 0;
    m_lastCleanupCount = 0;

    debug_printf("渐进式数据管理(纯优化): 所有数据已清空\n");
}

void MonitoringDataSource::addDataPoint(double o2, double co, int switch1, double relativeTimeHours, double relativeTimeMinutes)
{
    // 直接使用优化数据结构
    addDataPointOptimized(o2, co, switch1, relativeTimeHours, relativeTimeMinutes);
}

void MonitoringDataSource::addDataPointOptimized(double o2, double co, int switch1, double relativeTimeHours, double relativeTimeMinutes)
{
    // 资源保护：多级检查机制
    if (!m_resourceProtectionEnabled) {
        debug_printf("⚠️ 资源保护已禁用，跳过数据添加\n");
        return;
    }

    // 数据有效性检查
    if (std::isnan(o2) || std::isnan(co) || std::isnan(relativeTimeHours) || std::isnan(relativeTimeMinutes)) {
        debug_printf("⚠️ 检测到无效数据，跳过添加\n");
        return;
    }

    // 内存保护：检查数据点数量，防止无限增长
    if (m_totalDataPoints > CRITICAL_ERROR_THRESHOLD) {
        handleCriticalError(QString("数据点数量超过严重错误阈值: %1").arg(m_totalDataPoints));
        return;
    } else if (m_totalDataPoints > EMERGENCY_CLEANUP_THRESHOLD) {
        debug_printf("⚠️ 内存保护：数据点过多(%d)，执行紧急清理\n", m_totalDataPoints);
        performEmergencyCleanup();
    }

    // 纯优化数据添加 - 只使用高效结构体，避免QVariantMap开销
    m_o2DataOptimized.append(DataPoint(relativeTimeHours, relativeTimeMinutes, o2));
    m_coDataOptimized.append(DataPoint(relativeTimeHours, relativeTimeMinutes, co));
    m_switch1DataOptimized.append(DataPoint(relativeTimeHours, relativeTimeMinutes, switch1));

    m_totalDataPoints++;

    // 减少调试输出频率以提升性能
    if (m_totalDataPoints % 50 == 0) {  // 每50个点输出一次，进一步减少日志
        debug_printf("数据管理: 添加数据点 - 时间=%.3f小时, O2=%.2f%%, CO=%.0fppm, 总点数=%d\n",
                    relativeTimeHours, o2, co, m_totalDataPoints);
    }

    // 更积极的内存管理：更频繁地维护数据窗口
    if (m_totalDataPoints % 10 == 0) {  // 每10个点检查一次
        maintainDataWindowOptimized();
    }

    // 定期执行内存清理 - 降低阈值，更频繁清理
    if (m_totalDataPoints - m_lastCleanupCount >= (CLEANUP_THRESHOLD / 2)) {
        performMemoryCleanup();
        m_lastCleanupCount = m_totalDataPoints;
    }
}

// maintainDataWindow 方法已移除，使用 maintainDataWindowOptimized 替代

void MonitoringDataSource::performMemoryCleanup()
{
    // 优化版本的内存清理
    debug_printf("内存清理: 开始清理，当前数据点数=%d\n", m_o2DataOptimized.size());

    // 强制维护数据窗口
    maintainDataWindowOptimized();

    // 更积极的内存清理策略 - 保持更少的数据点
    if (m_o2DataOptimized.size() > MAX_MEMORY_POINTS * 0.6) {  // 从0.7降到0.6
        int keepCount = static_cast<int>(MAX_MEMORY_POINTS * 0.5);  // 保留50%
        int removeCount = m_o2DataOptimized.size() - keepCount;

        if (removeCount > 0) {
            // 批量移除，比逐个removeFirst高效
            m_o2DataOptimized.remove(0, removeCount);
            m_coDataOptimized.remove(0, removeCount);
            m_switch1DataOptimized.remove(0, removeCount);

            // 更新总数据点计数
            m_totalDataPoints = m_o2DataOptimized.size();

            debug_printf("内存清理: 清理了%d个数据点，剩余%d个\n",
                        removeCount, m_o2DataOptimized.size());
        }
    }

    // 表格数据也需要清理 - 限制表格行数
    if (m_smokeTableData.size() > MAX_TABLE_ROWS * 2) {
        while (m_smokeTableData.size() > MAX_TABLE_ROWS) {
            m_smokeTableData.removeLast();
        }
        debug_printf("内存清理: 清理了多余的表格数据，剩余%d行\n", m_smokeTableData.size());
    }

    // 定期强制垃圾回收 - 降低频率
    if (m_totalDataPoints % (CLEANUP_THRESHOLD * 5) == 0) {
        QCoreApplication::processEvents(QEventLoop::AllEvents, 30);  // 减少处理时间
        debug_printf("内存清理: 执行垃圾回收\n");
    }
}

void MonitoringDataSource::performEmergencyCleanup()
{
    debug_printf("🚨 紧急内存清理: 数据点过多，执行紧急清理\n");

    // 保留最近的数据点，大幅减少内存占用
    int keepCount = MAX_MEMORY_POINTS / 4;  // 只保留25%的数据

    if (m_o2DataOptimized.size() > keepCount) {
        int removeCount = m_o2DataOptimized.size() - keepCount;

        m_o2DataOptimized.remove(0, removeCount);
        m_coDataOptimized.remove(0, removeCount);
        m_switch1DataOptimized.remove(0, removeCount);

        // 重置计数器
        m_totalDataPoints = m_o2DataOptimized.size();
        m_lastCleanupCount = 0;

        debug_printf("紧急清理完成: 移除%d个数据点，剩余%d个\n",
                    removeCount, m_o2DataOptimized.size());
    }

    // 清理表格数据
    m_smokeTableData.clear();

    // 强制垃圾回收
    QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
}

void MonitoringDataSource::checkMemoryUsage()
{
    int totalDataPoints = m_o2DataOptimized.size();
    int tableRows = m_smokeTableData.size();

    // 估算内存使用量（粗略计算）
    int estimatedMemoryKB = (totalDataPoints * sizeof(DataPoint) * 3 +
                            tableRows * 200) / 1024;  // 每个表格行约200字节

    if (totalDataPoints > MAX_MEMORY_POINTS * 0.8) {
        debug_printf("⚠️ 内存警告: 数据点数=%d, 估算内存=%dKB\n",
                    totalDataPoints, estimatedMemoryKB);
    }

    // 分级内存管理
    if (totalDataPoints > CRITICAL_ERROR_THRESHOLD) {
        handleCriticalError(QString("数据点数量达到严重错误阈值: %1").arg(totalDataPoints));
    } else if (totalDataPoints > EMERGENCY_CLEANUP_THRESHOLD) {
        debug_printf("🚨 数据点数量超过紧急阈值，执行紧急清理\n");
        performEmergencyCleanup();
    } else if (totalDataPoints > MEMORY_WARNING_THRESHOLD) {
        debug_printf("⚠️ 内存警告: 数据点数=%d, 估算内存=%dKB\n",
                    totalDataPoints, estimatedMemoryKB);
        if (m_resourceProtectionEnabled) {
            performMemoryCleanup();
        }
    }
}

void MonitoringDataSource::enableResourceProtection()
{
    m_resourceProtectionEnabled = true;
    m_criticalErrorCount = 0;
    m_lastMemoryCheck = QDateTime::currentDateTime();

    debug_printf("✅ 资源保护机制已启用\n");
}

void MonitoringDataSource::handleCriticalError(const QString& error)
{
    m_criticalErrorCount++;
    debug_printf("🔥 严重错误 #%d: %s\n", m_criticalErrorCount, error.toUtf8().constData());

    // 执行紧急措施
    if (m_criticalErrorCount >= 3) {
        debug_printf("🛑 连续严重错误，停止数据采集\n");
        setIsRunning(false);
        clearAllData();
        m_criticalErrorCount = 0;
    } else {
        // 尝试恢复：执行紧急清理
        performEmergencyCleanup();

        // 降低更新频率
        if (m_timer && m_timer->interval() < 10000) {
            m_timer->setInterval(10000);  // 设置为10秒间隔
            debug_printf("🔧 降低更新频率至10秒，尝试恢复\n");
        }
    }
}

// 动态转换优化数据为QML兼容格式的属性访问器
QVariantList MonitoringDataSource::smokeO2Data() const
{
    // 动态转换优化数据为QVariantList
    QVariantList result;
    result.reserve(m_o2DataOptimized.size());

    for (const DataPoint& point : m_o2DataOptimized) {
        QVariantMap map;
        map["x"] = point.timeHours;
        map["x_minutes"] = point.timeMinutes;
        map["y"] = point.value;
        result.append(map);
    }
    return result;
}

QVariantList MonitoringDataSource::smokeCOData() const
{
    // 动态转换优化数据为QVariantList
    QVariantList result;
    result.reserve(m_coDataOptimized.size());

    for (const DataPoint& point : m_coDataOptimized) {
        QVariantMap map;
        map["x"] = point.timeHours;
        map["x_minutes"] = point.timeMinutes;
        map["y"] = point.value;
        result.append(map);
    }
    return result;
}

QVariantList MonitoringDataSource::smokeSwitch1Data() const
{
    // 动态转换优化数据为QVariantList
    QVariantList result;
    result.reserve(m_switch1DataOptimized.size());

    for (const DataPoint& point : m_switch1DataOptimized) {
        QVariantMap map;
        map["x"] = point.timeHours;
        map["x_minutes"] = point.timeMinutes;
        map["y"] = point.value;
        result.append(map);
    }
    return result;
}

void MonitoringDataSource::maintainDataWindowOptimized()
{
    // 高效数据窗口维护 - 避免频繁的removeFirst操作
    if (m_o2DataOptimized.isEmpty()) {
        return;
    }

    // 获取当前时间和窗口开始时间
    QDateTime currentTime = QDateTime::currentDateTime();
    if (m_windowStartTime.isNull()) {
        m_windowStartTime = currentTime;
    }

    // 计算数据窗口的时间范围（25小时）
    QDateTime windowCutoff = currentTime.addSecs(-DATA_WINDOW_HOURS * 3600);

    // 批量移除超出时间窗口的旧数据 - 避免逐个removeFirst
    int removeCount = 0;
    for (int i = 0; i < m_o2DataOptimized.size(); ++i) {
        // 计算绝对时间
        QDateTime pointTime = m_dataStartTime.addSecs(m_o2DataOptimized[i].timeHours * 3600);

        if (pointTime < windowCutoff) {
            removeCount++;
        } else {
            break;  // 数据是按时间顺序的，后面的都不需要删除
        }
    }

    // 批量移除，比逐个removeFirst高效得多
    if (removeCount > 0) {
        // 只维护优化数据结构
        m_o2DataOptimized.remove(0, removeCount);
        m_coDataOptimized.remove(0, removeCount);
        m_switch1DataOptimized.remove(0, removeCount);

        debug_printf("渐进式数据管理(纯优化): 批量移除了%d个旧数据点，当前点数=%d\n",
                    removeCount, m_o2DataOptimized.size());
    }

    // 限制最大内存点数（硬限制）- 批量移除
    if (m_o2DataOptimized.size() > MAX_MEMORY_POINTS) {
        int excessCount = m_o2DataOptimized.size() - MAX_MEMORY_POINTS;
        m_o2DataOptimized.remove(0, excessCount);
        m_coDataOptimized.remove(0, excessCount);
        m_switch1DataOptimized.remove(0, excessCount);

        debug_printf("渐进式数据管理(纯优化): 批量移除了%d个超量数据点\n", excessCount);
    }
}

void MonitoringDataSource::updateData()
{
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();
    static int updateCounter = 0;

    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeDiff = lastUpdateTime.msecsTo(currentTime);
    updateCounter++;

    // 减少调试输出频率
    if (updateCounter % 10 == 0) {  // 每10次更新输出一次
        debug_printf("UI数据更新 - 第%d次, 距离上次: %lldms, 间隔: %dms\n",
                    updateCounter, timeDiff, m_timer->interval());
    }

    // 智能内存检查：基于时间和更新次数
    QDateTime currentTime = QDateTime::currentDateTime();
    bool shouldCheckMemory = false;

    if (updateCounter % 30 == 0) {  // 每30次更新检查一次
        shouldCheckMemory = true;
    } else if (m_lastMemoryCheck.secsTo(currentTime) > 300) {  // 或者每5分钟检查一次
        shouldCheckMemory = true;
    }

    if (shouldCheckMemory) {
        checkMemoryUsage();
        m_lastMemoryCheck = currentTime;
    }

    updateSmokeData();
    m_dataCount++;

    lastUpdateTime = currentTime;
}

void MonitoringDataSource::updateSmokeData()
{
    // 检查是否有选择的烟气分析仪
    if (m_currentBoiler.isEmpty()) {
        return;
    }

    // 从硬件获取真实烟气数据
    float o2 = 0.0f, co = 0.0f;
    float current = 0.0f, voltage = 0.0f, temperature = 0.0f;
    int switch1 = 1;  // 开关量信号，默认关闭状态
    bool hardwareConnected = false;

    // 检查硬件连接状态
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    std::string deviceName = m_currentBoiler.toStdString();

    auto it = boiler_map.find(deviceName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->fd >= 0) {
        hardwareConnected = true;

        // 立即获取数据，不等待data_ready标志
        // 这样可以让UI更快地显示已采集到的参数值
        try {
            get_realtime_data(deviceName, &co, &o2, &current, &voltage, &temperature, &switch1);

            // 在检查反吹状态之前，先保存当前的O2和CO数值（作为潜在的暂停前数值）
            if (!m_isDataUpdateSuspended) {
                // 只在数据更新未暂停时更新暂停前的数值
                m_suspendedO2Value = QString::number(o2, 'f', 2) + "%";
                m_suspendedCOValue = QString::number(co, 'f', 0) + "ppm";
            }

            // 检查反吹反馈状态并处理氧气和一氧化碳数值更新控制
            checkBackflowStatus(switch1);

            // 如果数据更新被暂停，记录日志
            if (m_isDataUpdateSuspended) {
                debug_printf("监控系统: 反吹反馈运行中，氧气和一氧化碳数值保持暂停状态 - O2: %s, CO: %s\n",
                            m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
            }

            // 如果是首次获取到有效数据，记录日志
            if (!it->second->data_ready && (o2 > 0 || co > 0)) {
                debug_printf("UI开始显示烟气分析仪 %s 的部分数据\n", deviceName.c_str());
            }
        } catch (...) {
            // 如果数据采集函数出现异常，设置为无效数据
            debug_printf("烟气数据获取异常: %s\n", deviceName.c_str());
            o2 = co = current = voltage = temperature = 0.0f;
            switch1 = 0;  // 异常时设置为停止状态
        }
    }
#endif

    // 更新连接状态
    if (hardwareConnected) {
        // 硬件连接时直接使用真实数据，不做额外的有效性判断
        // 更新连接状态
        if (!m_isDataConnected) {
            m_isDataConnected = true;
            m_connectionStatus = "烟气分析仪已连接";
            debug_printf("UI连接状态: 烟气分析仪已连接\n");
            emit dataConnectionChanged();
        }


        // 获取当前时间
        QDateTime now = QDateTime::currentDateTime();

        // 如果是第一次采集数据，设置开始时间
        if (!m_dataStartTimeSet) {
            m_dataStartTime = now;
            m_dataStartTimeSet = true;
        }

        // 获取当前锅炉的采集间隔配置
        int collectionIntervalSeconds = 15; // 默认15秒
#ifdef ENABLE_HARDWARE_DATA
        extern std::unordered_map<std::string, Boiler*> boiler_map;
        extern ConfigManager* g_config_manager;

        std::string boilerName = m_currentBoiler.toStdString();
        auto it = boiler_map.find(boilerName);
        if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
            collectionIntervalSeconds = it->second->collection_interval;
        } else if (g_config_manager && g_config_manager->exists(boilerName, "CollectionInterval")) {
            collectionIntervalSeconds = g_config_manager->get<int>(boilerName, "CollectionInterval", 15);
        }
#endif

        // 使用采集间隔（避免编译器警告）
        Q_UNUSED(collectionIntervalSeconds);

        // 计算基于数据开始时间的相对时间
        // 确保时间轴从0开始，并且严格按照实际时间流逝计算
        QDateTime currentTime = QDateTime::currentDateTime();
        if (!m_dataStartTimeSet) {
            m_dataStartTime = currentTime;
            m_dataStartTimeSet = true;
            debug_printf("数据开始时间已设置: %s\n", m_dataStartTime.toString("yyyy-MM-dd HH:mm:ss").toStdString().c_str());
        }

        // 计算从数据开始到现在的实际时间差
        qint64 elapsedSeconds = m_dataStartTime.secsTo(currentTime);
        double relativeTimeHours = elapsedSeconds / 3600.0;  // 转换为小时
        double relativeTimeMinutes = elapsedSeconds / 60.0;  // 转换为分钟

        // 更新图表数据
        // 氧气和一氧化碳数据：在反吹期间使用暂停前的数值继续添加数据点
        if (!m_isDataUpdateSuspended) {
            // 正常期间：使用实时数值
            addDataPoint(o2, co, switch1, relativeTimeHours, relativeTimeMinutes);
            debug_printf("数据添加: 正常模式添加数据 - 时间=%.3f小时(%.1f分钟), O2=%.2f%%, CO=%.0fppm\n",
                        relativeTimeHours, relativeTimeMinutes, o2, co);
        } else {
            // 反吹期间：使用暂停前的数值继续添加数据点，保持曲线连续性
            // 从暂停的数值字符串中提取数字部分
            QString suspendedO2Str = m_suspendedO2Value;
            QString suspendedCOStr = m_suspendedCOValue;
            suspendedO2Str.remove("%");
            suspendedCOStr.remove("ppm");

            double suspendedO2 = suspendedO2Str.toDouble();
            double suspendedCO = suspendedCOStr.toDouble();

            addDataPoint(suspendedO2, suspendedCO, switch1, relativeTimeHours, relativeTimeMinutes);
            debug_printf("数据添加: 反吹模式添加数据 - 时间=%.3f小时, O2=%.2f%%(暂停前), CO=%.0fppm(暂停前)\n",
                        relativeTimeHours, suspendedO2, suspendedCO);
        }

        // 更新当前数据值
        m_currentTemperature = QString::number(temperature, 'f', 1) + "℃";
        m_currentVoltage = QString::number(voltage, 'f', 4) + "kPa";  // 压力值保留4位小数
        m_currentCurrent = QString::number(current, 'f', 3) + "A";

        // 增加表格更新计数器
        m_tableUpdateCounter++;

        // 每三次采集才更新一次表格数据
        if (m_tableUpdateCounter >= TABLE_UPDATE_INTERVAL) {
            m_tableUpdateCounter = 0;  // 重置计数器

            // 添加表格数据（每三次采集更新一次，但在反吹期间使用暂停前的O2和CO数值）
            if (m_isDataUpdateSuspended) {
                // 反吹期间：使用暂停前的O2和CO数值，其他参数使用实时数值
                // 从暂停的数值字符串中提取数字部分
                QString suspendedO2Str = m_suspendedO2Value;
                QString suspendedCOStr = m_suspendedCOValue;
                suspendedO2Str.remove("%");
                suspendedCOStr.remove("ppm");

                double suspendedO2 = suspendedO2Str.toDouble();
                double suspendedCO = suspendedCOStr.toDouble();

                addSmokeTableRow(suspendedO2, suspendedCO, temperature, voltage, current, switch1);
                debug_printf("监控系统: 反吹反馈运行中，表格数据使用暂停前的O2=%.2f, CO=%.0f (每3次采集更新)\n", suspendedO2, suspendedCO);
            } else {
                // 正常期间：使用实时数值
                addSmokeTableRow(o2, co, temperature, voltage, current, switch1);
                debug_printf("监控系统: 表格数据已更新 - O2=%.2f%%, CO=%.0fppm (每3次采集更新)\n", o2, co);
            }
        } else {
            debug_printf("监控系统: 表格更新计数器=%d/%d，跳过本次表格更新\n", m_tableUpdateCounter, TABLE_UPDATE_INTERVAL);
        }
    } else {
        // 硬件未连接
        if (m_isDataConnected) {
            m_isDataConnected = false;
            m_connectionStatus = "烟气分析仪未连接";
            debug_printf("UI连接状态: 烟气分析仪未连接\n");
            emit dataConnectionChanged();
        }

        // 硬件未连接时设置默认值
        m_currentTemperature = "0.0℃";
        m_currentVoltage = "0.0000kPa";
        m_currentCurrent = "0.000A";
        // 不添加任何数据到图表和表格
    }

    emit smokeDataChanged();
    emit currentDataChanged();

    // 只在有数据且硬件连接时发射图表更新信号，避免重复发射
    bool hasData = (m_o2DataOptimized.size() > 0);
    if (hasData && hardwareConnected) {
        emit chartDataUpdated();
    }

    // 调试信息：输出当前状态
    int dataPointCount = m_o2DataOptimized.size();
    debug_printf("监控系统状态: 硬件连接=%s, 数据连接=%s, 表格数据行数=%d, 图表数据点数=%d, 表格计数器=%d/%d\n",
                hardwareConnected ? "是" : "否",
                m_isDataConnected ? "是" : "否",
                m_smokeTableData.size(),
                dataPointCount,
                m_tableUpdateCounter,
                TABLE_UPDATE_INTERVAL);
}

void MonitoringDataSource::loadBoilerList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    m_boilerList.clear();

    // 从全局设备映射中获取烟气分析仪列表
    for (const auto& pair : boiler_map) {
        m_boilerList.append(QString::fromStdString(pair.first));
    }

    // 如果有烟气分析仪，设置第一个为默认选择
    if (!m_boilerList.isEmpty() && m_currentBoiler.isEmpty()) {
        m_currentBoiler = m_boilerList.first();
        debug_printf("设置默认烟气分析仪: '%s'\n", m_currentBoiler.toStdString().c_str());

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("默认设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit boilerListChanged();
    emit currentBoilerChanged();
#else
    // 如果硬件数据采集被禁用，提供默认烟气分析仪列表
    m_boilerList << "SmokeAnalyzer1" << "SmokeAnalyzer2";
    if (m_currentBoiler.isEmpty()) {
        m_currentBoiler = "SmokeAnalyzer1";
    }
    emit boilerListChanged();
    emit currentBoilerChanged();
#endif
}



void MonitoringDataSource::addSmokeTableRow(double o2, double co, double temperature, double voltage, double current, int switch1)
{
    QVariantMap row;
    row["time"] = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    row["o2"] = QString::number(o2, 'f', 3);
    row["co"] = QString::number(co, 'f', 0);
    row["temperature"] = QString::number(temperature, 'f', 1);
    row["voltage"] = QString::number(voltage, 'f', 1);
    row["current"] = QString::number(current, 'f', 3);
    row["switch1"] = QString::number(switch1);

    m_smokeTableData.prepend(row);

    // 保持最多5行数据
    if (m_smokeTableData.size() > MAX_TABLE_ROWS) {
        m_smokeTableData.removeLast();
    }

    debug_printf("表格数据已添加: 时间=%s, O2=%.3f%%, CO=%.0fppm, 总行数=%d (每3次采集更新)\n",
                row["time"].toString().toStdString().c_str(),
                o2, co, m_smokeTableData.size());

    emit smokeTableDataChanged();
}


void MonitoringDataSource::updateSmokeChartSeries(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex)
{
    // 大幅减少调试输出频率
    static int updateCallCount = 0;
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();

    updateCallCount++;
    QDateTime currentTime = QDateTime::currentDateTime();

    // 防抖机制：避免过于频繁的图表更新
    if (lastUpdateTime.msecsTo(currentTime) < 5000) {  // 5秒内不重复更新
        if (updateCallCount % 20 == 0) {  // 每20次调用输出一次防抖信息
            debug_printf("图表更新: 防抖跳过，距离上次更新%lldms\n",
                        lastUpdateTime.msecsTo(currentTime));
        }
        return;
    }

    lastUpdateTime = currentTime;

    if (updateCallCount % 10 == 0) {  // 每10次调用输出一次
        debug_printf("图表更新: 第%d次调用, zoomIndex=%d, 数据点数=%d\n",
                    updateCallCount, zoomIndex, m_o2DataOptimized.size());
    }

    if (!o2Series || !coSeries) {
        debug_printf("图表更新: 错误 - 图表系列指针为空\n");
        return;
    }

    // 检查数据是否为空
    if (m_o2DataOptimized.isEmpty()) {
        if (updateCallCount % 20 == 0) {  // 减少空数据警告频率
            debug_printf("图表更新: 无数据可显示\n");
        }
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();

    // 根据缩放级别确定显示的时间范围和最大点数限制
    double displayRangeHours = 24.0;  // 默认24小时
    int maxDisplayPoints = ZOOM_24H_POINTS;  // 默认点数限制

    switch(zoomIndex) {
        case 0:
            displayRangeHours = 24.0;
            maxDisplayPoints = ZOOM_24H_POINTS;  // 150点
            break;
        case 1:
            displayRangeHours = 12.0;
            maxDisplayPoints = ZOOM_12H_POINTS;  // 200点
            break;
        case 2:
            displayRangeHours = 8.0;
            maxDisplayPoints = ZOOM_8H_POINTS;   // 250点
            break;
        case 3:
            displayRangeHours = 1.0;
            maxDisplayPoints = ZOOM_1H_POINTS;   // 300点
            break;
        default:
            displayRangeHours = 24.0;
            maxDisplayPoints = ZOOM_24H_POINTS;
            break;
    }

    debug_printf("图表更新(点数限制): zoomIndex=%d, 显示范围=%.1f小时, 最大点数=%d\n",
                zoomIndex, displayRangeHours, maxDisplayPoints);

    // 高效的时间偏移计算
    double timeOffset = 0.0;
    if (!m_o2DataOptimized.isEmpty()) {
        // 直接访问结构体成员，避免QVariantMap查找
        const DataPoint& lastPoint = m_o2DataOptimized.last();

        double currentTime;
        if (zoomIndex == 3) {
            // 1小时视图：使用分钟作为基准
            currentTime = lastPoint.timeMinutes;
            if (currentTime > 60.0) {
                timeOffset = (currentTime - 60.0) / 60.0;  // 转换为小时单位
            }
        } else {
            // 其他视图：使用小时作为基准
            currentTime = lastPoint.timeHours;
            if (currentTime > displayRangeHours) {
                timeOffset = currentTime - displayRangeHours;
            }
        }

        // 减少调试输出
        if (updateCallCount % 10 == 0) {
            debug_printf("时间偏移计算(纯优化): 当前时间=%.3f, 显示范围=%.1f, 偏移量=%.3f\n",
                        currentTime, displayRangeHours, timeOffset);
        }
    }

    // 高效数据填充 - 进一步优化版本
    const int dataSize = m_o2DataOptimized.size();

    // 预分配空间以提升性能，使用更保守的点数限制
    QVector<QPointF> o2Points, coPoints;
    int reserveSize = qMin(maxDisplayPoints, dataSize);  // 避免过度分配
    o2Points.reserve(reserveSize);
    coPoints.reserve(reserveSize);

    // 优化的数据抽样算法 - 减少计算开销
    int samplingInterval = 1;

    // 简化抽样计算：基于数据总量和显示点数的比例
    if (dataSize > maxDisplayPoints) {
        samplingInterval = (dataSize / maxDisplayPoints) + 1;
        // 进一步优化：对于大数据集使用更大的抽样间隔
        if (dataSize > maxDisplayPoints * 3) {
            samplingInterval = (dataSize / (maxDisplayPoints / 2)) + 1;
        }

        if (updateCallCount % 20 == 0) {  // 减少日志输出
            debug_printf("数据抽样: 总数据=%d, 最大显示=%d, 抽样间隔=%d\n",
                        dataSize, maxDisplayPoints, samplingInterval);
        }
    }

    // 优化的单次遍历处理 - 减少计算和内存分配
    int addedPoints = 0;
    int skipCount = 0;

    // 计算起始索引，跳过明显超出范围的数据
    int startIndex = 0;
    if (timeOffset > 0 && dataSize > 100) {
        // 粗略估算起始位置，避免从头遍历
        startIndex = qMax(0, static_cast<int>(dataSize * 0.1));
    }

    for (int i = startIndex; i < dataSize && addedPoints < maxDisplayPoints; i += samplingInterval) {
        const DataPoint& o2Point = m_o2DataOptimized[i];
        const DataPoint& coPoint = m_coDataOptimized[i];

        qreal absoluteTime, displayTime, maxDisplayTime;

        if (zoomIndex == 3) {
            // 1小时视图：使用分钟
            absoluteTime = o2Point.timeMinutes;
            displayTime = absoluteTime - (timeOffset * 60.0);
            maxDisplayTime = 60.0;
        } else {
            // 其他视图：使用小时
            absoluteTime = o2Point.timeHours;
            displayTime = absoluteTime - timeOffset;
            maxDisplayTime = displayRangeHours;
        }

        // 只处理在显示范围内的点
        if (displayTime >= 0.0 && displayTime <= maxDisplayTime) {
            o2Points.append(QPointF(displayTime, o2Point.value));
            coPoints.append(QPointF(displayTime, coPoint.value));
            addedPoints++;
        } else if (displayTime > maxDisplayTime) {
            // 超出范围，可以提前退出
            skipCount++;
            if (skipCount > 10) break;  // 连续跳过10个点后退出
        }
    }

    // 高效批量更新 - 只在有数据时更新
    if (!o2Points.isEmpty()) {
        // 检查是否需要更新：避免重复相同的数据
        bool needUpdate = true;
        if (o2LineSeries->count() == o2Points.size() && o2Points.size() > 3) {
            // 简单检查：比较最后几个点
            auto existingPoints = o2LineSeries->pointsVector();
            if (existingPoints.size() >= 2 && o2Points.size() >= 2) {
                QPointF lastExisting = existingPoints.last();
                QPointF lastNew = o2Points.last();
                if (qAbs(lastExisting.x() - lastNew.x()) < 0.01 &&
                    qAbs(lastExisting.y() - lastNew.y()) < 0.01) {
                    needUpdate = false;
                }
            }
        }

        if (needUpdate) {
            o2LineSeries->replace(o2Points);
            coLineSeries->replace(coPoints);
        } else if (updateCallCount % 30 == 0) {
            debug_printf("图表更新: 数据未变化，跳过更新\n");
        }
    }

    // 减少调试输出频率
    if (updateCallCount % 15 == 0) {
        debug_printf("图表更新完成: zoom=%d, 范围=%.1fh, O2点数=%d, CO点数=%d\n",
                    zoomIndex, displayRangeHours, o2LineSeries->count(), coLineSeries->count());
    }
}

void MonitoringDataSource::updateSmokeChartSeriesWithMinutes(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries)
{
    debug_printf("图表更新(分钟视图): updateSmokeChartSeriesWithMinutes被调用, O2数据点数=%d\n", m_o2DataOptimized.size());

    if (!o2Series || !coSeries) {
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();

    // 1小时分钟视图的32位程序性能优化参数
    int samplingInterval = 6;   // 每6个点取1个（3秒×6=18秒间隔，32位优化）
    int maxPoints = 200;        // 最多200个点（32位程序优化）

    debug_printf("图表性能优化(分钟视图-32位): 抽样间隔=%d, 最大点数=%d, 原始数据点数=%d\n",
                samplingInterval, maxPoints, m_o2DataOptimized.size());

    // 计算分钟时间偏移量：当数据超过60分钟时，让曲线向左移动
    double timeOffsetMinutes = 0.0;

    if (!m_o2DataOptimized.isEmpty()) {
        double currentTimeMinutes = m_o2DataOptimized.last().timeMinutes;

        // 如果当前时间超过60分钟，计算偏移量让最新数据显示在60分钟位置
        if (currentTimeMinutes > 60.0) {
            timeOffsetMinutes = currentTimeMinutes - 60.0;
        }

        debug_printf("分钟视图时间偏移计算: 当前时间=%.1f分钟, 显示范围=60分钟, 偏移量=%.1f分钟\n",
                    currentTimeMinutes, timeOffsetMinutes);
    }

    // 计算有效的数据范围：只处理在显示窗口内或附近的数据
    int dataPointCount = m_o2DataOptimized.size();
    int startIndex = 0;
    int endIndex = dataPointCount;

    // 如果有时间偏移，找到合适的起始索引以提高性能
    if (timeOffsetMinutes > 0 && dataPointCount > 50) {
        for (int i = 0; i < dataPointCount; i++) {
            qreal absoluteTimeMinutes = m_o2DataOptimized[i].timeMinutes;
            qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;

            if (displayTimeMinutes >= -5.0) {  // 留5分钟缓冲
                startIndex = qMax(0, i - samplingInterval);  // 往前退一点确保不遗漏
                break;
            }
        }
    }

    // 添加O2数据（使用优化的起始索引和抽样）
    int addedO2Points = 0;
    for (int i = startIndex; i < endIndex && addedO2Points < maxPoints; i += samplingInterval) {
        const DataPoint& point = m_o2DataOptimized[i];
        qreal absoluteTimeMinutes = point.timeMinutes;
        qreal value = point.value;

        qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;

        // 只显示在0-60范围内的数据点
        if (displayTimeMinutes >= 0.0 && displayTimeMinutes <= 60.0) {
            o2LineSeries->append(displayTimeMinutes, value);
            addedO2Points++;
        }

        // 如果已经超出显示范围太多，可以提前退出
        if (displayTimeMinutes > 65.0) {
            break;
        }
    }

    // 添加CO数据（使用相同的优化逻辑）
    int addedCOPoints = 0;
    for (int i = startIndex; i < endIndex && addedCOPoints < maxPoints; i += samplingInterval) {
        const DataPoint& point = m_coDataOptimized[i];
        qreal absoluteTimeMinutes = point.timeMinutes;
        qreal value = point.value;

        qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;

        if (displayTimeMinutes >= 0.0 && displayTimeMinutes <= 60.0) {
            coLineSeries->append(displayTimeMinutes, value);
            addedCOPoints++;
        }

        // 如果已经超出显示范围太多，可以提前退出
        if (displayTimeMinutes > 65.0) {
            break;
        }
    }

    debug_printf("图表更新完成(分钟视图): O2系列点数=%d (优化后), CO系列点数=%d (优化后), 时间偏移=%.1f分钟\n",
                o2LineSeries->count(), coLineSeries->count(), timeOffsetMinutes);

    // 额外的调试信息：显示第一个和最后一个数据点的时间
    if (o2LineSeries->count() > 0) {
        auto points = o2LineSeries->pointsVector();
        if (!points.isEmpty()) {
            debug_printf("分钟视图时间范围: 第一个点=%.1f分钟, 最后一个点=%.1f分钟, 时间跨度=%.1f分钟\n",
                        points.first().x(), points.last().x(), points.last().x() - points.first().x());
        }
    }
}

void MonitoringDataSource::updateSmokeChartSeriesWithScroll(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex, double scrollOffset)
{
    if (!o2Series || !coSeries) {
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();

    // 根据缩放级别确定显示的时间范围和最大点数限制
    double displayRangeHours = 24.0;  // 默认24小时
    int maxDisplayPoints = ZOOM_24H_POINTS;  // 默认点数限制

    switch(zoomIndex) {
        case 0:
            displayRangeHours = 24.0;
            maxDisplayPoints = ZOOM_24H_POINTS;  // 150点
            break;
        case 1:
            displayRangeHours = 12.0;
            maxDisplayPoints = ZOOM_12H_POINTS;  // 200点
            break;
        case 2:
            displayRangeHours = 8.0;
            maxDisplayPoints = ZOOM_8H_POINTS;   // 250点
            break;
        case 3:
            displayRangeHours = 1.0;
            maxDisplayPoints = ZOOM_1H_POINTS;   // 300点
            break;
        default:
            displayRangeHours = 24.0;
            maxDisplayPoints = ZOOM_24H_POINTS;
            break;
    }

    // 使用手动滚动偏移量而不是自动计算
    double timeOffset = scrollOffset;

    debug_printf("滚动图表更新(点数限制): zoomIndex=%d, 显示范围=%.1f小时, 最大点数=%d, 滚动偏移=%.3f\n",
                zoomIndex, displayRangeHours, maxDisplayPoints, scrollOffset);

    // 计算数据抽样间隔，确保不超过最大点数限制
    const int dataSize = m_o2DataOptimized.size();
    int samplingInterval = 1;
    int validDataCount = 0;

    // 先快速计算在显示范围内的数据点数量
    for (int i = 0; i < dataSize; ++i) {
        const DataPoint& point = m_o2DataOptimized[i];
        qreal absoluteTime = point.timeHours;
        qreal displayTime = absoluteTime - timeOffset;

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            validDataCount++;
        }
    }

    // 如果数据点过多，计算抽样间隔
    if (validDataCount > maxDisplayPoints) {
        samplingInterval = (validDataCount / maxDisplayPoints) + 1;
        debug_printf("滚动图表抽样优化: 有效数据点=%d, 最大显示点=%d, 抽样间隔=%d\n",
                    validDataCount, maxDisplayPoints, samplingInterval);
    }

    // 添加O2数据（应用点数限制和抽样）
    int sampleCounter = 0;
    int addedO2Points = 0;
    for (int i = 0; i < dataSize && addedO2Points < maxDisplayPoints; ++i) {
        const DataPoint& point = m_o2DataOptimized[i];
        qreal absoluteTime = point.timeHours;
        qreal displayTime = absoluteTime - timeOffset;  // 减去偏移量
        qreal value = point.value;

        // 只显示在显示范围内的数据点
        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            // 应用抽样：只有满足抽样间隔的点才添加
            if (sampleCounter % samplingInterval == 0) {
                o2LineSeries->append(displayTime, value);
                addedO2Points++;
            }
            sampleCounter++;
        }
    }

    // 添加CO数据（应用点数限制和抽样）
    sampleCounter = 0;
    int addedCOPoints = 0;
    for (int i = 0; i < dataSize && addedCOPoints < maxDisplayPoints; ++i) {
        const DataPoint& point = m_coDataOptimized[i];
        qreal absoluteTime = point.timeHours;
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point.value;

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            // 应用抽样：只有满足抽样间隔的点才添加
            if (sampleCounter % samplingInterval == 0) {
                coLineSeries->append(displayTime, value);
                addedCOPoints++;
            }
            sampleCounter++;
        }
    }

    debug_printf("滚动图表更新完成: O2系列点数=%d, CO系列点数=%d (限制=%d点)\n",
                addedO2Points, addedCOPoints, maxDisplayPoints);
}

void MonitoringDataSource::reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler)
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    if (newBoiler.isEmpty()) {
        debug_printf("新烟气分析仪名称为空，跳过串口重新初始化\n");
        return;
    }

    std::string newDeviceName = newBoiler.toStdString();
    debug_printf("开始为烟气分析仪 '%s' 重新初始化串口连接\n", newDeviceName.c_str());

    // 查找新烟气分析仪
    auto newIt = boiler_map.find(newDeviceName);
    if (newIt == boiler_map.end() || newIt->second == nullptr) {
        debug_printf("错误: 找不到烟气分析仪 '%s'，设备映射大小: %zu\n", newDeviceName.c_str(), boiler_map.size());
        return;
    }

    Boiler* newDeviceObj = newIt->second;
    debug_printf("找到烟气分析仪 '%s'，当前fd: %d，协议: '%s'\n",
                newDeviceName.c_str(), newDeviceObj->fd, newDeviceObj->protocol.c_str());

    // 检查新烟气分析仪的串口连接状态
    if (newDeviceObj->fd < 0) {
        // 串口未连接，需要重新初始化
        debug_printf("烟气分析仪 '%s' 串口未连接，开始重新初始化串口连接\n", newDeviceName.c_str());

        // 获取配置管理器
        extern ConfigManager* g_config_manager;
        if (g_config_manager == nullptr) {
            debug_printf("错误: 全局配置管理器未初始化\n");
            return;
        }

        try {
            // 重新获取协议配置并初始化串口
            std::string protocol = newDeviceObj->protocol;
            if (protocol.empty()) {
                // 如果协议为空，从配置文件重新读取
                protocol = g_config_manager->get<std::string>(newDeviceName, "Protocol");
                newDeviceObj->protocol = protocol;
                debug_printf("从配置文件重新读取烟气分析仪 '%s' 的协议: '%s'\n", newDeviceName.c_str(), protocol.c_str());
            }

            // 获取协议对应的串口配置
            std::string port = g_config_manager->get<std::string>(protocol, "Port");
            int baud_rate = g_config_manager->get<int>(protocol, "BaudRate", 9600);
            char parity = g_config_manager->get<char>(protocol, "Parity");
            int stop_bits = g_config_manager->get<int>(protocol, "StopBits", 1);
            int data_bits = g_config_manager->get<int>(protocol, "DataBits", 8);

            debug_printf("重新初始化烟气分析仪 '%s' 串口配置:\n", newDeviceName.c_str());
            debug_printf("  协议: '%s'\n", protocol.c_str());
            debug_printf("  端口: '%s'\n", port.c_str());
            debug_printf("  波特率: %d\n", baud_rate);
            debug_printf("  校验位: %c\n", parity);
            debug_printf("  停止位: %d\n", stop_bits);
            debug_printf("  数据位: %d\n", data_bits);

            // 重新打开串口
            extern int open_serial_port(const char *device, int speed, char parity, int stop_bits, int data_bits);
            int new_fd = open_serial_port(port.c_str(), baud_rate, parity, stop_bits, data_bits);

            if (new_fd >= 0) {
                newDeviceObj->fd = new_fd;
                debug_printf("烟气分析仪 '%s' 串口重新初始化成功，文件描述符: %d\n", newDeviceName.c_str(), new_fd);
            } else {
                debug_printf("烟气分析仪 '%s' 串口重新初始化失败\n", newDeviceName.c_str());
            }
        } catch (const std::exception& e) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生异常: %s\n", newDeviceName.c_str(), e.what());
        } catch (...) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生未知异常\n", newDeviceName.c_str());
        }
    } else {
        debug_printf("烟气分析仪 '%s' 串口已连接，文件描述符: %d\n", newDeviceName.c_str(), newDeviceObj->fd);
    }

    // 输出切换完成信息
    if (!oldBoiler.isEmpty()) {
        debug_printf("烟气分析仪切换完成: 从 '%s' 切换到 '%s'\n", oldBoiler.toStdString().c_str(), newDeviceName.c_str());
    } else {
        debug_printf("初始化烟气分析仪 '%s' 完成\n", newDeviceName.c_str());
    }

#else
    debug_printf("硬件数据采集被禁用，跳过串口重新初始化\n");
#endif
}

void MonitoringDataSource::updateTimerInterval()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    extern ConfigManager* g_config_manager;

    if (g_config_manager == nullptr) {
        debug_printf("⚠ 配置管理器未初始化，无法设置UI更新间隔\n");
        return;
    }

    // 如果没有选择锅炉，尝试从第一个可用锅炉获取配置
    std::string boilerName;
    if (m_currentBoiler.isEmpty()) {
        if (!boiler_map.empty()) {
            boilerName = boiler_map.begin()->first;
            debug_printf("调试: 没有选择锅炉，使用第一个可用锅炉 '%s' 的配置\n", boilerName.c_str());
        } else {
            debug_printf("⚠ 没有可用的锅炉配置\n");
            return;
        }
    } else {
        boilerName = m_currentBoiler.toStdString();
    }

    // 首先尝试从锅炉对象获取采集间隔
    auto it = boiler_map.find(boilerName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
        int collectionInterval = it->second->collection_interval;
        int uiInterval = collectionInterval * 1000; // 转换为毫秒，与采集间隔保持一致
        m_timer->setInterval(uiInterval);
        debug_printf("设置UI更新间隔: %d毫秒 (采集间隔: %d秒)\n", uiInterval, collectionInterval);
        return;
    }

    // 如果锅炉对象不可用，直接从配置文件读取
    if (g_config_manager->exists(boilerName, "CollectionInterval")) {
        int collectionInterval = g_config_manager->get<int>(boilerName, "CollectionInterval");
        if (collectionInterval > 0) {
            int uiInterval = collectionInterval * 1000; // 转换为毫秒，与采集间隔保持一致
            m_timer->setInterval(uiInterval);
            debug_printf("设置UI更新间隔: %d毫秒 (采集间隔: %d秒)\n", uiInterval, collectionInterval);
            return;
        }
    }
#endif
}

void MonitoringDataSource::updateChartIncremental(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex)
{
    // 减少调试输出以提升性能
    static int updateCount = 0;
    updateCount++;

    if (updateCount % 20 == 0) {  // 每20次更新才输出一次日志
        debug_printf("渐进式图表更新(纯优化): 第%d次调用, zoomIndex=%d, 数据点数=%d\n",
                    updateCount, zoomIndex, m_o2DataOptimized.size());
    }

    // 检查数据和系列有效性
    if (!o2Series || !coSeries || m_o2DataOptimized.isEmpty()) {
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 根据缩放级别确定最大显示点数（32位程序优化）
    int maxDisplayPoints = 150;  // 32位程序默认点数
    switch(zoomIndex) {
        case 0: maxDisplayPoints = ZOOM_24H_POINTS; break;  // 24小时：150点（32位优化）
        case 1: maxDisplayPoints = ZOOM_12H_POINTS; break;  // 12小时：200点（32位优化）
        case 2: maxDisplayPoints = ZOOM_8H_POINTS; break;   // 8小时：250点（32位优化）
        case 3: maxDisplayPoints = ZOOM_1H_POINTS; break;   // 1小时：300点（32位优化）
        default: maxDisplayPoints = ZOOM_24H_POINTS; break;
    }

    // 如果图表为空，进行全量更新
    if (o2LineSeries->count() == 0) {
        updateSmokeChartSeries(o2Series, coSeries, zoomIndex);
        return;
    }

    // 计算时间偏移量（与updateSmokeChartSeries保持一致）
    double timeOffset = 0.0;
    double displayRangeHours = 24.0;
    switch(zoomIndex) {
        case 0: displayRangeHours = 24.0; break;
        case 1: displayRangeHours = 12.0; break;
        case 2: displayRangeHours = 8.0; break;
        case 3: displayRangeHours = 1.0; break;
        default: displayRangeHours = 24.0; break;
    }

    // 高效的时间偏移计算
    if (!m_o2DataOptimized.isEmpty()) {
        // 直接访问结构体
        const DataPoint& lastPoint = m_o2DataOptimized.last();

        if (zoomIndex == 3) {
            // 1小时视图：使用分钟作为基准
            if (lastPoint.timeMinutes > 60.0) {
                timeOffset = (lastPoint.timeMinutes - 60.0) / 60.0;
            }
        } else {
            // 其他视图：使用小时作为基准
            if (lastPoint.timeHours > displayRangeHours) {
                timeOffset = lastPoint.timeHours - displayRangeHours;
            }
        }
    }

    // 高效的增量更新：只添加最新的1-2个数据点
    int dataSize = m_o2DataOptimized.size();
    int startIndex = qMax(0, dataSize - 2);  // 只取最新的2个点

    for (int i = startIndex; i < dataSize; ++i) {
        const DataPoint& o2Point = m_o2DataOptimized[i];
        const DataPoint& coPoint = m_coDataOptimized[i];

        // 统一时间计算逻辑
        double absoluteTime, displayTime, maxDisplayTime;

        if (zoomIndex == 3) {
            // 1小时视图：使用分钟
            absoluteTime = o2Point.timeMinutes;
            displayTime = absoluteTime - (timeOffset * 60.0);
            maxDisplayTime = 60.0;
        } else {
            // 其他视图：使用小时
            absoluteTime = o2Point.timeHours;
            displayTime = absoluteTime - timeOffset;
            maxDisplayTime = displayRangeHours;
        }

        // 只添加在显示范围内的点
        if (displayTime >= 0.0 && displayTime <= maxDisplayTime) {
            appendNewChartPoint(o2Series, displayTime, o2Point.value, maxDisplayPoints);
            appendNewChartPoint(coSeries, displayTime, coPoint.value, maxDisplayPoints);
        }
    }

    if (updateCount % 10 == 0) {
        debug_printf("渐进式图表更新完成: O2系列点数=%d, CO系列点数=%d\n",
                    o2LineSeries->count(), coLineSeries->count());
    }
}

void MonitoringDataSource::appendNewChartPoint(QtCharts::QAbstractSeries *series, double x, double y, int maxPoints)
{
    QtCharts::QLineSeries *lineSeries = qobject_cast<QtCharts::QLineSeries*>(series);
    if (!lineSeries) {
        return;
    }

    // 如果达到最大点数，移除最旧的点
    if (lineSeries->count() >= maxPoints) {
        lineSeries->removePoints(0, 1);
    }

    // 添加新点
    lineSeries->append(x, y);
}
