import QtQuick 2.12
import QtCharts 2.3

Rectangle {
    id: root
    width: 800
    height: 600
    
    property bool isRealTimeMode: true  // 控制实时/历史模式
    property int currentZoomIndex: 0    // 当前缩放级别
    
    Column {
        anchors.fill: parent
        spacing: 10
        
        // 模式切换按钮
        Row {
            spacing: 10
            anchors.horizontalCenter: parent.horizontalCenter
            
            Rectangle {
                width: 120
                height: 40
                color: isRealTimeMode ? "#4CAF50" : "#E0E0E0"
                border.color: "#333"
                radius: 5
                
                Text {
                    anchors.centerIn: parent
                    text: "实时模式"
                    color: isRealTimeMode ? "white" : "black"
                }
                
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        isRealTimeMode = true
                        updateChart()
                    }
                }
            }
            
            Rectangle {
                width: 120
                height: 40
                color: !isRealTimeMode ? "#FF9800" : "#E0E0E0"
                border.color: "#333"
                radius: 5
                
                Text {
                    anchors.centerIn: parent
                    text: "历史模式"
                    color: !isRealTimeMode ? "white" : "black"
                }
                
                MouseArea {
                    anchors.fill: parent
                    onClicked: {
                        isRealTimeMode = false
                        updateChart()
                    }
                }
            }
        }
        
        // 时间范围选择按钮
        Row {
            spacing: 10
            anchors.horizontalCenter: parent.horizontalCenter
            
            Repeater {
                model: ["24小时", "12小时", "8小时", "1小时"]
                
                Rectangle {
                    width: 80
                    height: 35
                    color: currentZoomIndex === index ? "#2196F3" : "#F5F5F5"
                    border.color: "#333"
                    radius: 3
                    
                    Text {
                        anchors.centerIn: parent
                        text: modelData
                        color: currentZoomIndex === index ? "white" : "black"
                        font.pixelSize: 12
                    }
                    
                    MouseArea {
                        anchors.fill: parent
                        onClicked: {
                            currentZoomIndex = index
                            updateChart()
                        }
                    }
                }
            }
        }
        
        // 图表区域
        ChartView {
            id: chartView
            width: parent.width - 20
            height: parent.height - 120
            anchors.horizontalCenter: parent.horizontalCenter
            
            title: isRealTimeMode ? "实时数据 - 固定窗口" : "历史数据 - 可滚动"
            titleColor: isRealTimeMode ? "#4CAF50" : "#FF9800"
            
            LineSeries {
                id: o2Series
                name: "O2浓度"
                color: "#FF5722"
                width: 2
            }
            
            LineSeries {
                id: coSeries
                name: "CO浓度"
                color: "#2196F3"
                width: 2
            }
            
            ValueAxis {
                id: xAxis
                titleText: currentZoomIndex === 3 ? "时间 (分钟)" : "时间 (小时)"
                min: 0
                max: getTimeRange()
            }
            
            ValueAxis {
                id: yAxis
                titleText: "浓度"
                min: 0
                max: 100
            }
        }
        
        // 状态信息
        Text {
            anchors.horizontalCenter: parent.horizontalCenter
            text: getStatusText()
            color: "#666"
            font.pixelSize: 14
        }
    }
    
    // 定时器：实时模式下定期更新
    Timer {
        id: realTimeTimer
        interval: 2000  // 2秒更新一次
        running: isRealTimeMode
        repeat: true
        onTriggered: updateChart()
    }
    
    function getTimeRange() {
        switch(currentZoomIndex) {
            case 0: return 24  // 24小时
            case 1: return 12  // 12小时
            case 2: return 8   // 8小时
            case 3: return 60  // 60分钟
            default: return 24
        }
    }
    
    function getStatusText() {
        var mode = isRealTimeMode ? "实时模式" : "历史模式"
        var range = ["24小时", "12小时", "8小时", "1小时"][currentZoomIndex]
        var behavior = isRealTimeMode ? "固定窗口，无滚动" : "支持滚动查看"
        return mode + " | " + range + " | " + behavior
    }
    
    function updateChart() {
        if (!monitoringDataSource) {
            console.log("数据源未初始化")
            return
        }
        
        if (isRealTimeMode) {
            // 实时模式：使用固定窗口方法
            if (currentZoomIndex === 3) {
                // 1小时视图
                monitoringDataSource.updateRealTimeChartSeriesWithMinutes(o2Series, coSeries)
            } else {
                // 其他时间范围
                monitoringDataSource.updateRealTimeChartSeries(o2Series, coSeries, currentZoomIndex)
            }
            console.log("实时模式更新: " + ["24h", "12h", "8h", "1h"][currentZoomIndex] + " 固定窗口")
        } else {
            // 历史模式：使用滚动方法
            if (currentZoomIndex === 3) {
                // 1小时视图
                monitoringDataSource.updateSmokeChartSeriesWithMinutes(o2Series, coSeries)
            } else {
                // 其他时间范围
                monitoringDataSource.updateSmokeChartSeries(o2Series, coSeries, currentZoomIndex)
            }
            console.log("历史模式更新: " + ["24h", "12h", "8h", "1h"][currentZoomIndex] + " 可滚动")
        }
    }
    
    Component.onCompleted: {
        updateChart()
    }
}
